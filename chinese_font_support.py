#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中文字体支持模块
用于设置matplotlib中文字体，确保图表中的中文正确显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import sys

def setup_chinese_font():
    """
    设置同时支持中文和英文的字体，确保图表中的所有文本正确显示
    返回: 字体属性对象或None
    """
    # 查找系统中可能支持中文的字体
    fonts = fm.findSystemFonts()

    # 按优先级查找可能同时支持中文和英文的字体
    font_candidates = [
        # 优先使用同时支持中英文的字体
        ('Droid Sans Fallback', lambda f: 'Droid' in f and 'Fallback' in f),
        ('Noto Sans CJK', lambda f: 'Noto' in f and 'CJK' in f),
        ('Source Han Sans', lambda f: ('Source' in f and 'Han' in f) or 'SourceHan' in f),
        ('WenQuanYi Micro Hei', lambda f: 'WenQuanYi' in f and 'Micro' in f),
        ('WenQuanYi Zen Hei', lambda f: 'WenQuanYi' in f and 'Zen' in f),
        ('Microsoft YaHei', lambda f: 'Microsoft' in f and 'YaHei' in f),
        ('SimHei', lambda f: 'SimHei' in f),
        ('SimSun', lambda f: 'SimSun' in f),
        ('FangSong', lambda f: 'FangSong' in f),
        # 任何其他可能的字体
        ('Any WenQuanYi', lambda f: 'WenQuanYi' in f),
        ('Any Droid Sans', lambda f: 'Droid' in f and 'Sans' in f and not 'Mono' in f),
        ('DejaVu Sans', lambda f: 'DejaVu' in f and 'Sans' in f and not 'Mono' in f),
        ('Noto Sans', lambda f: 'Noto' in f and 'Sans' in f and not 'Mono' in f),
        ('Any Sans', lambda f: 'Sans' in f and not 'Mono' in f)
    ]

    # 打印系统中的所有字体
    print("系统中的所有字体:")
    for i, font in enumerate(fonts[:10]):  # 只打印前10个，避免输出过多
        print(f"  {i+1}. {os.path.basename(font)}")
    if len(fonts) > 10:
        print(f"  ... 以及其他 {len(fonts)-10} 个字体")

    # 尝试每个候选字体
    for name, condition in font_candidates:
        matching_fonts = [f for f in fonts if condition(f)]
        if matching_fonts:
            for font_path in matching_fonts:
                font_name = name
                print(f"尝试字体: {name} ({font_path})")

                # 创建字体属性对象
                try:
                    font_prop = fm.FontProperties(fname=font_path)

                    # 测试字体是否支持中文和英文
                    test_chars = "测试中文English123"
                    unsupported_chars = []

                    try:
                        # 检查字体是否支持所有测试字符
                        for char in test_chars:
                            try:
                                if not font_prop.get_file().supports_char(ord(char)):
                                    unsupported_chars.append(char)
                            except:
                                pass

                        if unsupported_chars:
                            print(f"  警告: 字体不支持以下字符: {''.join(unsupported_chars)}")
                            # 如果不支持中文字符，尝试下一个字体
                            if any(ord(c) > 127 for c in unsupported_chars):
                                print(f"  字体不支持中文，尝试下一个字体")
                                continue
                    except:
                        print("  无法检查字体字符支持情况")

                    # 设置全局字体
                    plt.rcParams['font.family'] = 'sans-serif'
                    plt.rcParams['font.sans-serif'] = [font_prop.get_name(), 'DejaVu Sans', 'Droid Sans', 'FreeSans']

                    # 解决负号显示问题
                    plt.rcParams['axes.unicode_minus'] = False

                    print(f"成功设置字体: {font_name}")
                    return font_prop
                except Exception as e:
                    print(f"  设置字体时出错: {str(e)}")
                    continue

    # 如果没有找到任何支持中文的字体，使用系统默认字体

    # 如果没有找到支持中文的字体，使用系统默认字体
    print("未找到支持中文的字体，尝试使用系统默认字体")

    # 设置字体回退机制
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Droid Sans', 'FreeSans', 'sans-serif']

    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False

    # 尝试使用matplotlib的内置字体
    try:
        # 使用matplotlib的内置字体
        font_names = [f.name for f in fm.fontManager.ttflist]
        for font in ['DejaVu Sans', 'Arial', 'Helvetica', 'Tahoma', 'Verdana']:
            if font in font_names:
                print(f"使用matplotlib内置字体: {font}")
                return fm.FontProperties(family=font)
    except:
        pass

    return None

# 全局字体对象
chinese_font = setup_chinese_font()

def apply_chinese_font_to_plot(title=None, xlabel=None, ylabel=None, fontsize=12):
    """
    应用中文字体到当前图表

    参数:
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        fontsize: 字体大小
    """
    if title:
        plt.title(title, fontproperties=chinese_font if chinese_font else None, fontsize=fontsize)
    if xlabel:
        plt.xlabel(xlabel, fontproperties=chinese_font if chinese_font else None, fontsize=fontsize)
    if ylabel:
        plt.ylabel(ylabel, fontproperties=chinese_font if chinese_font else None, fontsize=fontsize)

# 测试函数
def test_chinese_font():
    """测试中英文字体是否正常工作"""
    import numpy as np

    # 创建一个简单的图表
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)

    plt.figure(figsize=(12, 8))
    plt.plot(x, y, linewidth=2)

    # 设置标题和标签（中英文混合）
    apply_chinese_font_to_plot(
        title='中英文字体测试 - Sine Wave Test',
        xlabel='角度 (Angle in Radians)',
        ylabel='振幅 (Amplitude)'
    )
    plt.grid(True)

    # 添加中英文混合文本
    if chinese_font:
        plt.text(np.pi/4, 0.8, '中文测试 Chinese Test', fontsize=14, fontproperties=chinese_font)
        plt.text(np.pi/2, 0.5, 'English Text 英文测试', fontsize=14, fontproperties=chinese_font)
        plt.text(3*np.pi/4, 0.2, '数字和符号: -123.45, +67.89', fontsize=14, fontproperties=chinese_font)
        plt.text(np.pi, -0.2, '特殊符号: @#$%^&*()', fontsize=14, fontproperties=chinese_font)
        plt.text(5*np.pi/4, -0.5, '混合文本 Mixed Text 123', fontsize=14, fontproperties=chinese_font)
        plt.text(3*np.pi/2, -0.8, '测试完成 Test Complete', fontsize=14, fontproperties=chinese_font)
    else:
        plt.text(np.pi/4, 0.8, '中文测试 Chinese Test', fontsize=14)
        plt.text(np.pi/2, 0.5, 'English Text 英文测试', fontsize=14)
        plt.text(3*np.pi/4, 0.2, '数字和符号: -123.45, +67.89', fontsize=14)
        plt.text(np.pi, -0.2, '特殊符号: @#$%^&*()', fontsize=14)
        plt.text(5*np.pi/4, -0.5, '混合文本 Mixed Text 123', fontsize=14)
        plt.text(3*np.pi/2, -0.8, '测试完成 Test Complete', fontsize=14)

    # 添加图例（中英文混合）
    plt.plot(x, np.cos(x), 'r--', linewidth=2, label='余弦曲线 Cosine')
    plt.plot(x, 0.5*np.sin(2*x), 'g-.', linewidth=2, label='双倍正弦 Double Sine')
    plt.legend(prop=chinese_font, fontsize=12, loc='upper right')

    # 保存图表
    output_dir = 'font_test_output'
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(f'{output_dir}/mixed_font_test.png', dpi=150)
    print(f"测试图表已保存到 {output_dir}/mixed_font_test.png")

    # 创建第二个测试图表 - 专门测试标签和标题
    plt.figure(figsize=(14, 10))

    # 创建4个子图，测试不同的字体设置
    plt.subplot(2, 2, 1)
    plt.plot(x, np.sin(x))
    plt.title('纯中文标题', fontproperties=chinese_font)
    plt.xlabel('纯中文横轴', fontproperties=chinese_font)
    plt.ylabel('纯中文纵轴', fontproperties=chinese_font)

    plt.subplot(2, 2, 2)
    plt.plot(x, np.cos(x))
    plt.title('Pure English Title')
    plt.xlabel('Pure English X-Axis')
    plt.ylabel('Pure English Y-Axis')

    plt.subplot(2, 2, 3)
    plt.plot(x, np.sin(2*x))
    plt.title('混合标题 Mixed Title', fontproperties=chinese_font)
    plt.xlabel('混合横轴 Mixed X-Axis', fontproperties=chinese_font)
    plt.ylabel('混合纵轴 Mixed Y-Axis', fontproperties=chinese_font)

    plt.subplot(2, 2, 4)
    plt.plot(x, np.cos(2*x))
    plt.title('数字和符号: -123.45, +67.89', fontproperties=chinese_font)
    plt.xlabel('特殊符号: @#$%^&*()', fontproperties=chinese_font)
    plt.ylabel('混合: 中文English123', fontproperties=chinese_font)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/font_test_subplots.png', dpi=150)
    print(f"子图测试已保存到 {output_dir}/font_test_subplots.png")

if __name__ == "__main__":
    test_chinese_font()
