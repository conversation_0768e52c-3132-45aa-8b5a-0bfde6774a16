#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
这个脚本用于修改BiLSTM+TA（无bug）.py文件，添加中文字体支持。
它会在文件中添加必要的代码，使matplotlib能够正确显示中文字符。
"""

import os
import re

# 要修改的文件路径
file_path = 'BiLSTM+TA（无bug）.py'

# 备份原始文件
backup_path = file_path + '.bak'
if not os.path.exists(backup_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"已创建备份文件: {backup_path}")

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 要添加的中文字体支持代码
chinese_font_code = '''
# =================== 添加中文字体支持 ===================
def setup_chinese_font():
    """设置中文字体，确保图表中的中文正确显示"""
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    import os
    
    # 查找系统中可能支持中文的字体
    fonts = fm.findSystemFonts()
    
    # 优先查找Droid Sans Fallback字体（通常支持中文）
    droid_fallback_fonts = [f for f in fonts if 'Droid' in f and 'Fallback' in f]
    if droid_fallback_fonts:
        print(f"使用系统字体: Droid Sans Fallback")
        font_path = droid_fallback_fonts[0]
        font_prop = fm.FontProperties(fname=font_path)
    else:
        # 尝试查找其他可能支持中文的字体
        chinese_fonts = []
        for f in fonts:
            if any(name in f for name in ['Droid', 'WenQuanYi', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'Microsoft YaHei']):
                chinese_fonts.append(f)
        
        if chinese_fonts:
            font_path = chinese_fonts[0]
            font_name = os.path.basename(font_path)
            print(f"使用系统字体: {font_name}")
            font_prop = fm.FontProperties(fname=font_path)
        else:
            # 如果没有找到支持中文的字体，使用系统默认字体
            print("未找到支持中文的字体，使用系统默认字体")
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Droid Sans', 'FreeSans', 'sans-serif']
            return None
    
    # 设置全局字体
    plt.rcParams['font.family'] = font_prop.get_name()
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    return font_prop

# 初始化中文字体
chinese_font = setup_chinese_font()
'''

# 检查文件中是否已经包含中文字体支持代码
if 'setup_chinese_font' not in content:
    # 在导入语句之后添加中文字体支持代码
    import_pattern = r'import matplotlib\.pyplot as plt'
    if re.search(import_pattern, content):
        content = re.sub(import_pattern, 'import matplotlib.pyplot as plt' + chinese_font_code, content, count=1)
    else:
        # 如果没有找到matplotlib导入语句，在文件开头添加
        content = chinese_font_code + '\n' + content

    # 修改所有plt.title, plt.xlabel, plt.ylabel调用，添加fontproperties参数
    title_pattern = r'plt\.title\(([^)]+)\)'
    content = re.sub(title_pattern, r'plt.title(\1, fontproperties=chinese_font if chinese_font else None)', content)
    
    xlabel_pattern = r'plt\.xlabel\(([^)]+)\)'
    content = re.sub(xlabel_pattern, r'plt.xlabel(\1, fontproperties=chinese_font if chinese_font else None)', content)
    
    ylabel_pattern = r'plt\.ylabel\(([^)]+)\)'
    content = re.sub(ylabel_pattern, r'plt.ylabel(\1, fontproperties=chinese_font if chinese_font else None)', content)
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已成功添加中文字体支持到文件: {file_path}")
else:
    print(f"文件 {file_path} 已经包含中文字体支持代码")

print("完成！")
