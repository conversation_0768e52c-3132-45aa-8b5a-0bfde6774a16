#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试使用英文标签
"""

import matplotlib.pyplot as plt
import numpy as np
import os

def test_english_labels():
    """测试使用英文标签"""
    # 创建输出目录
    output_dir = 'font_test_output'
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 测试1: 简单折线图
    plt.figure(figsize=(12, 6))
    plt.plot(x, y1, 'b-', label='Sine')
    plt.plot(x, y2, 'r--', label='Cosine')
    plt.title('Mixed Title (English Only)')
    plt.xlabel('X-Axis')
    plt.ylabel('Y-Axis')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'{output_dir}/test1_english_line_chart.png', dpi=150)
    plt.close()
    
    # 测试2: 柱状图
    categories = ['Category A', 'Category B', 'Category C', 'Category D']
    values = [25, 40, 30, 55]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(categories, values, color=['blue', 'green', 'red', 'purple'])
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                 f'{height}',
                 ha='center', va='bottom')
    
    plt.title('Bar Chart (English Only)')
    plt.xlabel('Categories')
    plt.ylabel('Values')
    plt.savefig(f'{output_dir}/test2_english_bar_chart.png', dpi=150)
    plt.close()
    
    print(f"所有测试图表已保存到 {output_dir} 目录")

if __name__ == "__main__":
    test_english_labels()
