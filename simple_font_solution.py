#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的字体解决方案
使用matplotlib的字体回退机制，确保图表中的文本正确显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os

def setup_fonts():
    """
    设置matplotlib字体，确保图表中的文本正确显示
    """
    # 设置字体回退机制
    plt.rcParams['font.family'] = ['sans-serif']
    
    # 尝试找到系统中可能支持中文的字体
    fonts = fm.findSystemFonts()
    
    # 可能支持中文的字体名称
    chinese_font_names = []
    
    # 检查系统中的字体
    print("检查系统中的字体...")
    for font_path in fonts:
        try:
            font_name = os.path.basename(font_path)
            if any(name in font_path for name in ['WenQuanYi', 'wqy', 'Noto', 'CJK', 'Source', 'Han', 'SimSun', 'SimHei', 'Microsoft', 'YaHei', 'Droid']):
                chinese_font_names.append(font_name)
                print(f"找到可能支持中文的字体: {font_name}")
        except:
            pass
    
    # 设置字体回退顺序
    fallback_fonts = ['DejaVu Sans', 'Bitstream Vera Sans', 'Droid Sans', 'FreeSans', 'Arial', 'Helvetica', 'sans-serif']
    
    # 将可能支持中文的字体添加到回退列表的前面
    all_fonts = chinese_font_names + fallback_fonts
    
    # 设置sans-serif字体族
    plt.rcParams['font.sans-serif'] = all_fonts
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    print(f"字体设置完成，使用以下字体回退顺序: {', '.join(all_fonts[:5])}...")

def apply_font_to_plot(title=None, xlabel=None, ylabel=None, fontsize=12):
    """
    应用字体到当前图表
    
    参数:
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        fontsize: 字体大小
    """
    if title:
        plt.title(title, fontsize=fontsize)
    if xlabel:
        plt.xlabel(xlabel, fontsize=fontsize)
    if ylabel:
        plt.ylabel(ylabel, fontsize=fontsize)

def test_fonts():
    """测试字体设置是否正常工作"""
    import numpy as np
    
    # 设置字体
    setup_fonts()
    
    # 创建一个简单的图表
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    
    plt.figure(figsize=(10, 6))
    plt.plot(x, y)
    apply_font_to_plot(
        title='中英文混合标题 Mixed Title',
        xlabel='X轴 (角度) X-Axis (Angle)',
        ylabel='Y轴 (振幅) Y-Axis (Amplitude)'
    )
    plt.grid(True)
    
    # 添加一些中英文混合文本
    plt.text(np.pi/2, 0.5, '中文测试 Chinese Test', fontsize=12)
    plt.text(np.pi, -0.5, '负数: -123.45', fontsize=12)
    
    # 保存图表
    output_dir = 'font_test_output'
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(f'{output_dir}/simple_font_test.png', dpi=100)
    print(f"测试图表已保存到 {output_dir}/simple_font_test.png")

if __name__ == "__main__":
    test_fonts()
