import matplotlib.font_manager as fm
import matplotlib.pyplot as plt
import numpy as np
import os

# 获取所有可用字体
fonts = sorted([f.name for f in fm.fontManager.ttflist])
print(f"系统中共有 {len(fonts)} 个字体")

# 创建一个测试中文显示的函数
def test_font(font_name):
    fig, ax = plt.subplots(figsize=(10, 2))
    ax.text(0.5, 0.5, f"测试中文字体: {font_name} - 水库水位监测",
            fontsize=14, fontfamily=font_name, ha='center', va='center')
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')

    # 保存图片
    output_dir = 'font_test'
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(f"{output_dir}/{font_name.replace(' ', '_')}.png", dpi=100)
    plt.close()

    return f"{output_dir}/{font_name.replace(' ', '_')}.png"

# 测试常见的中文字体
chinese_font_candidates = [
    'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi',
    'Microsoft YaHei', 'Microsoft JhengHei',
    'Source Han Sans CN', 'Source Han Serif CN',
    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei',
    'Noto Sans CJK SC', 'Noto Sans CJK TC', 'Noto Sans CJK JP',
    'Noto Serif CJK SC', 'Noto Serif CJK TC', 'Noto Serif CJK JP',
    'AR PL UMing CN', 'AR PL UKai CN',
    'DejaVu Sans', 'DejaVu Serif'
]

# 添加系统中所有包含以下关键词的字体
keywords = ['han', 'hei', 'kai', 'ming', 'song', 'gothic', 'mincho',
            'sim', 'noto', 'cjk', 'chinese', 'sc', 'tc', 'jp', 'kr']

for font in fonts:
    lower_font = font.lower()
    if any(keyword in lower_font for keyword in keywords) and font not in chinese_font_candidates:
        chinese_font_candidates.append(font)

# 去重
chinese_font_candidates = list(set(chinese_font_candidates))
print(f"测试 {len(chinese_font_candidates)} 个可能支持中文的字体")

# 测试这些字体
available_fonts = []
for font in chinese_font_candidates:
    if font in fonts:
        print(f"测试字体: {font}")
        test_font(font)
        available_fonts.append(font)
    else:
        print(f"字体不可用: {font}")

print("\n可用的可能支持中文的字体:")
for font in available_fonts:
    print(f"- {font}")

# 创建一个展示所有可用字体的图表
if available_fonts:
    fig, axes = plt.subplots(len(available_fonts), 1, figsize=(12, len(available_fonts)*1.2))
    if len(available_fonts) == 1:
        axes = [axes]

    for i, font in enumerate(available_fonts):
        axes[i].text(0.5, 0.5, f"测试中文字体: {font} - 水库水位监测",
                fontsize=12, fontfamily=font, ha='center', va='center')
        axes[i].set_xlim(0, 1)
        axes[i].set_ylim(0, 1)
        axes[i].set_ylabel(font, rotation=0, labelpad=40, ha='right', va='center')
        axes[i].axis('off')

    plt.tight_layout()
    plt.savefig("all_chinese_fonts.png", dpi=150, bbox_inches='tight')
    print("\n所有字体预览已保存到 all_chinese_fonts.png")

# 输出 matplotlib 的字体缓存路径
try:
    print(f"\nmatplotlib 字体缓存路径: {fm.get_cachedir()}")
except AttributeError:
    print("\nmatplotlib 字体缓存路径: 无法获取 (get_cachedir 方法不可用)")

# 输出 matplotlib 的配置路径
import matplotlib
print(f"matplotlib 配置路径: {matplotlib.get_configdir()}")

# 输出当前 matplotlib 的字体设置
print("\n当前 matplotlib 字体设置:")
print(f"font.family: {plt.rcParams['font.family']}")
print(f"font.sans-serif: {plt.rcParams['font.sans-serif']}")
print(f"axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")

# 创建一个推荐的字体配置
print("\n推荐的字体配置:")
for font in available_fonts:
    print(f"""
# 使用 {font} 字体
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['{font}', 'DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = True
    """)
