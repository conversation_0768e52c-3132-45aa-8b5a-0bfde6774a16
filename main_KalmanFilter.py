import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Custom simple Kalman filter implementation, replacing p<PERSON><PERSON><PERSON>
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os
from datetime import datetime
# Import scipy.signal module, using alias to avoid conflicts
from scipy import signal as scipy_signal
import warnings
import time  # replacing tqdm

# Suppress TensorFlow warnings
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=default, 1=info, 2=warning, 3=error

# Check if GPU acceleration can be used
try:
    # Suppress TensorFlow warnings and error messages
    import warnings
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=UserWarning)

    import tensorflow as tf
    # Disable TensorFlow logs
    tf.get_logger().setLevel('ERROR')

    # Detect and configure GPU
    def setup_gpu():
        """Detect and configure GPU"""
        print("Detecting GPU...")
        # Set TensorFlow log level
        tf.autograph.set_verbosity(0)

        # Disable unnecessary TensorFlow logs
        import logging
        logging.getLogger('tensorflow').setLevel(logging.ERROR)

        # Detect GPU
        gpus = tf.config.list_physical_devices('GPU')

        if gpus:
            try:
                # Set GPU memory growth
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)

                # Set visible GPU
                tf.config.set_visible_devices(gpus, 'GPU')

                logical_gpus = tf.config.list_logical_devices('GPU')
                print(f"Detected {len(gpus)} physical GPUs, {len(logical_gpus)} logical GPUs")
                print("GPU configuration successful, will use GPU for computation")
                return True
            except RuntimeError as e:
                # Memory growth must be set at program start
                print(f"GPU configuration error: {e}")
                return False
        else:
            print("No GPU detected, will use CPU for computation")
            return False

    # Set up GPU
    HAS_GPU = setup_gpu()

    # If GPU is available, use TensorFlow to accelerate matrix operations
    if HAS_GPU:
        print("Will use TensorFlow to accelerate matrix operations")

        # Use TensorFlow to accelerate matrix multiplication, ensuring dimension matching
        def matrix_multiply(a, b):
            # Ensure a and b are numpy arrays
            a_np = np.asarray(a)
            b_np = np.asarray(b)

            # Get shapes
            a_shape = a_np.shape
            b_shape = b_np.shape

            # Handle vector and matrix multiplication
            if len(a_shape) == 2 and len(b_shape) == 1:
                # Matrix x Vector
                b_reshaped = b_np.reshape(-1, 1)
                result = np.matmul(a_np, b_reshaped)
                return result.flatten()
            elif len(a_shape) == 1 and len(b_shape) == 2:
                # Vector x Matrix
                a_reshaped = a_np.reshape(1, -1)
                result = np.matmul(a_reshaped, b_np)
                return result.flatten()
            elif len(a_shape) == 1 and len(b_shape) == 1:
                # Vector x Vector (dot product)
                return np.dot(a_np, b_np)
            else:
                # Normal matrix multiplication
                return np.matmul(a_np, b_np)

        # Use TensorFlow to accelerate matrix inversion
        def matrix_inverse(a):
            # Ensure a is a 2D matrix
            if len(np.shape(a)) == 2:
                return np.linalg.inv(a)
            else:
                # If not a 2D matrix, reshape first
                a_shape = np.shape(a)
                a_reshaped = np.reshape(a, (a_shape[0], a_shape[0]))
                result = np.linalg.inv(a_reshaped)
                return result
    else:
        # Use NumPy for matrix operations
        def matrix_multiply(a, b):
            # Ensure a and b are numpy arrays
            a_np = np.asarray(a)
            b_np = np.asarray(b)

            # Get shapes
            a_shape = a_np.shape
            b_shape = b_np.shape

            # Handle vector and matrix multiplication
            if len(a_shape) == 2 and len(b_shape) == 1:
                # Matrix x Vector
                b_reshaped = b_np.reshape(-1, 1)
                result = np.matmul(a_np, b_reshaped)
                return result.flatten()
            elif len(a_shape) == 1 and len(b_shape) == 2:
                # Vector x Matrix
                a_reshaped = a_np.reshape(1, -1)
                result = np.matmul(a_reshaped, b_np)
                return result.flatten()
            elif len(a_shape) == 1 and len(b_shape) == 1:
                # Vector x Vector (dot product)
                return np.dot(a_np, b_np)
            else:
                # Normal matrix multiplication
                return np.matmul(a_np, b_np)

        def matrix_inverse(a):
            # Ensure a is a 2D matrix
            if len(np.shape(a)) == 2:
                return np.linalg.inv(a)
            else:
                # If not a 2D matrix, reshape first
                a_shape = np.shape(a)
                a_reshaped = np.reshape(a, (a_shape[0], a_shape[0]))
                result = np.linalg.inv(a_reshaped)
                return result
except ImportError:
    print("TensorFlow not installed, cannot use GPU acceleration")
    HAS_GPU = False

    # Use NumPy for matrix operations
    def matrix_multiply(a, b):
        # Ensure a and b are numpy arrays
        a_np = np.asarray(a)
        b_np = np.asarray(b)

        # Get shapes
        a_shape = a_np.shape
        b_shape = b_np.shape

        # Handle vector and matrix multiplication
        if len(a_shape) == 2 and len(b_shape) == 1:
            # Matrix x Vector
            b_reshaped = b_np.reshape(-1, 1)
            result = np.matmul(a_np, b_reshaped)
            return result.flatten()
        elif len(a_shape) == 1 and len(b_shape) == 2:
            # Vector x Matrix
            a_reshaped = a_np.reshape(1, -1)
            result = np.matmul(a_reshaped, b_np)
            return result.flatten()
        elif len(a_shape) == 1 and len(b_shape) == 1:
            # Vector x Vector (dot product)
            return np.dot(a_np, b_np)
        else:
            # Normal matrix multiplication
            return np.matmul(a_np, b_np)

    def matrix_inverse(a):
        # Ensure a is a 2D matrix
        if len(np.shape(a)) == 2:
            return np.linalg.inv(a)
        else:
            # If not a 2D matrix, reshape first
            a_shape = np.shape(a)
            a_reshaped = np.reshape(a, (a_shape[0], a_shape[0]))
            result = np.linalg.inv(a_reshaped)
            return result

# 增强版卡尔曼滤波器实现
def enhanced_kalman_filter(data, Q, R, alpha=1.0, beta=0.0, model_type='constant'):
    """
    增强版一维卡尔曼滤波器实现，支持多种状态转移模型和双向滤波

    参数:
    - data: 输入数据序列
    - Q: 过程噪声方差
    - R: 观测噪声方差
    - alpha: 平滑因子 (0-1)
    - beta: 趋势权重 (0-1)，用于线性模型
    - model_type: 状态转移模型类型 ('constant', 'linear', 'adaptive')

    返回:
    - After Filtering的数据序列
    """
    # 参数验证和限制
    n = len(data)
    if n == 0:
        return np.array([])

    # 确保参数在有效范围内
    Q = max(1e-10, min(1.0, Q))  # 限制Q在合理范围内
    R = max(1e-10, min(1.0, R))  # 限制R在合理范围内
    alpha = max(0.1, min(1.0, alpha))  # 限制alpha在0.1到1.0之间
    beta = max(0.0, min(0.5, beta))  # 限制beta在0.0到0.5之间

    # 初始化滤波结果数组
    filtered = np.zeros(n)

    # 处理无效数据
    if np.any(~np.isfinite(data)):
        print("警告: 输入数据包含NaN或无穷大，将被替换为0")
        data_clean = np.copy(data)
        data_clean[~np.isfinite(data_clean)] = 0
    else:
        data_clean = data

    # 根据模型类型设置状态维度
    if model_type == 'constant':
        # 常数模型：只跟踪位置
        state_dim = 1
        F = np.array([[1.0]])  # 状态转移矩阵
    elif model_type == 'linear':
        # 线性模型：跟踪位置和速度
        state_dim = 2
        F = np.array([[1.0, 1.0], [0.0, 1.0]])  # 状态转移矩阵
    else:  # 'adaptive'
        # 自适应模型：根据数据特性动态调整
        state_dim = 1
        F = np.array([[1.0]])  # 初始状态转移矩阵

    # 初始状态
    if state_dim == 1:
        x = np.array([data_clean[0]])  # 初始状态估计 (位置)
        P = np.array([[1.0]])    # 初始估计误差协方差
    else:  # state_dim == 2
        x = np.array([data_clean[0], 0.0])  # 初始状态估计 (位置, 速度)
        P = np.array([[1.0, 0.0], [0.0, 1.0]])  # 初始估计误差协方差

    # 观测矩阵
    if state_dim == 1:
        H = np.array([[1.0]])
    else:  # state_dim == 2
        H = np.array([[1.0, 0.0]])  # 只观测位置

    # 过程噪声协方差矩阵
    if state_dim == 1:
        Q_mat = np.array([[Q]])
    else:  # state_dim == 2
        Q_mat = np.array([[Q, 0.0], [0.0, Q/10.0]])  # 速度噪声较小

    # 观测噪声协方差
    R_mat = np.array([[R]])

    # 保存第一个点
    filtered[0] = x[0]

    # 前向滤波
    try:
        for i in range(1, n):
            # 自适应模型：根据数据变化调整状态转移矩阵
            if model_type == 'adaptive':
                # 计算最近几个点的趋势
                window = min(5, i)
                if window > 0:
                    recent_trend = (data_clean[i] - data_clean[i-window]) / window
                    # 限制趋势值在合理范围内
                    recent_trend = max(-0.5, min(0.5, recent_trend))
                    # 调整状态转移矩阵
                    F[0, 0] = 1.0 + beta * np.sign(recent_trend) * min(0.1, abs(recent_trend))

            # 预测步骤
            try:
                x_pred = matrix_multiply(F, x)  # 状态预测
                P_pred = matrix_multiply(F, matrix_multiply(P, F.T)) + Q_mat  # 预测误差协方差

                # 确保P_pred是正定的
                if state_dim == 1:
                    P_pred = np.array([[max(1e-10, P_pred[0, 0])]])
                else:
                    # 对角线元素必须为正
                    P_pred[0, 0] = max(1e-10, P_pred[0, 0])
                    P_pred[1, 1] = max(1e-10, P_pred[1, 1])
                    # 确保行列式为正
                    det = P_pred[0, 0] * P_pred[1, 1] - P_pred[0, 1] * P_pred[1, 0]
                    if det <= 0:
                        # 如果行列式不为正，重置为对角矩阵
                        P_pred = np.array([[P_pred[0, 0], 0.0], [0.0, P_pred[1, 1]]])

                # 计算卡尔曼增益
                S = matrix_multiply(H, matrix_multiply(P_pred, H.T)) + R_mat  # 创新协方差

                # 确保S是可逆的
                if state_dim == 1:
                    S = np.array([[max(1e-10, S[0, 0])]])
                    S_inv = np.array([[1.0 / S[0, 0]]])
                else:
                    # 确保S的对角元素为正
                    S[0, 0] = max(1e-10, S[0, 0])
                    # 计算逆矩阵
                    S_inv = matrix_inverse(S)

                K = matrix_multiply(P_pred, matrix_multiply(H.T, S_inv))  # 卡尔曼增益

                # 更新步骤
                z = np.array([data_clean[i]])  # 观测值
                y = z - matrix_multiply(H, x_pred)  # 创新/残差
                x_update = x_pred + matrix_multiply(K, y)  # 状态更新

                # 检查更新后的状态是否有效
                if np.all(np.isfinite(x_update)):
                    x = x_update
                else:
                    # 如果更新无效，使用预测值
                    x = x_pred

                # 更新误差协方差
                I_KH = np.eye(state_dim) - matrix_multiply(K, H)
                P_update = matrix_multiply(I_KH, P_pred)

                # 确保P是对称的
                if state_dim > 1:
                    P_update = (P_update + P_update.T) / 2

                # 检查更新后的协方差是否有效
                if np.all(np.isfinite(P_update)):
                    P = P_update
                else:
                    # 如果更新无效，重置为单位矩阵
                    P = np.eye(state_dim)

                # 保存滤波结果（位置）
                filtered[i] = x[0]
            except Exception as e:
                # 如果计算出错，使用前一个值
                print(f"滤波计算出错 (i={i}): {str(e)}")
                filtered[i] = filtered[i-1]
    except Exception as e:
        print(f"滤波过程出错: {str(e)}")
        # 如果整个过程出错，返回Original Data
        return np.array(data_clean)

    # 应用额外的平滑 (如果需要)
    if alpha < 1.0:
        try:
            # 指数平滑
            smoothed = np.copy(filtered)
            for i in range(1, n):
                smoothed[i] = alpha * filtered[i] + (1 - alpha) * smoothed[i-1]

            # 双向滤波：再次从后向前滤波以减少相位延迟
            backward_smoothed = np.copy(smoothed)
            for i in range(n-2, -1, -1):
                backward_smoothed[i] = alpha * smoothed[i] + (1 - alpha) * backward_smoothed[i+1]

            # 合并前向和后向滤波结果
            final_smoothed = (smoothed + backward_smoothed) / 2

            # 检查结果是否有效
            if np.all(np.isfinite(final_smoothed)):
                return final_smoothed
            else:
                print("平滑结果包含NaN或无穷大，返回未平滑的结果")
                return filtered
        except Exception as e:
            print(f"平滑过程出错: {str(e)}")
            return filtered

    return filtered

# 增强版兼容性包装器类
class KalmanFilter:
    """增强版兼容性包装器，使用增强版卡尔曼滤波器实现"""
    def __init__(self, transition_matrices=None, observation_matrices=None, initial_state_mean=None,
                 initial_state_covariance=None, transition_covariance=None, observation_covariance=None,
                 alpha=0.8, beta=0.1, model_type='adaptive'):
        """
        初始化卡尔曼滤波器

        参数:
        - transition_matrices: 状态转移矩阵（不使用，仅为兼容性保留）
        - observation_matrices: 观测矩阵（不使用，仅为兼容性保留）
        - initial_state_mean: 初始状态均值（不使用，仅为兼容性保留）
        - initial_state_covariance: 初始状态协方差（不使用，仅为兼容性保留）
        - transition_covariance: 过程噪声协方差
        - observation_covariance: 观测噪声协方差
        - alpha: 平滑因子 (0-1)
        - beta: 趋势权重 (0-1)
        - model_type: 状态转移模型类型 ('constant', 'linear', 'adaptive')
        """
        self.Q = float(transition_covariance) if transition_covariance is not None else 1e-4
        self.R = float(observation_covariance) if observation_covariance is not None else 1e-2
        self.alpha = alpha
        self.beta = beta
        self.model_type = model_type
        self.residue = None

    def filter(self, measurements):
        """
        对测量数据进行滤波

        参数:
        - measurements: 测量数据序列

        返回:
        - filtered_state: After Filtering的状态序列
        - None: 占位符，保持API兼容性
        """
        # 使用增强版实现
        filtered = enhanced_kalman_filter(
            measurements,
            self.Q,
            self.R,
            alpha=self.alpha,
            beta=self.beta,
            model_type=self.model_type
        )

        # 计算残差（噪声）
        self.residue = measurements - filtered

        # 转换为列向量以保持API兼容性
        filtered_state = filtered.reshape(-1, 1)
        return filtered_state, None

# 忽略警告
warnings.filterwarnings('ignore')


# Set font and plot style - use Noto Sans CJK JP font for Chinese characters
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = True
plt.style.use('seaborn-v0_8-whitegrid')  # Use more attractive plotting style


# 1. 读取数据并处理时间列缺失值
def load_data(file_path='第一列Original Data.xlsx'):
    """加载并预处理数据"""
    try:
        print(f"正在读取数据: {file_path}")
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)

        # 检查列名
        print(f"文件包含以下列: {df.columns.tolist()}")

        # 确保有Date列
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
        else:
            # 尝试找到Date列
            for col in df.columns:
                if pd.api.types.is_datetime64_any_dtype(df[col]) or 'date' in col.lower() or 'time' in col.lower():
                    print(f"将列 '{col}' 作为Date列")
                    df['date'] = pd.to_datetime(df[col])
                    df.set_index('date', inplace=True)
                    df = df.drop(col, axis=1, errors='ignore')
                    break
            else:
                # 如果没有找到Date列，创建一个
                print("未找到Date列，创建默认Date索引")
                df['date'] = pd.date_range(start='2020-01-01', periods=len(df), freq='D')
                df.set_index('date', inplace=True)

        # 确保有Displacement数据列
        if 'displacement_1' in df.columns:
            displacement_col = 'displacement_1'
        elif 'G1' in df.columns:
            displacement_col = 'G1'
            print(f"使用 'G1' 列作为Displacement数据")
        else:
            # 使用第一列作为Displacement数据
            displacement_col = df.columns[0]
            print(f"使用第一列 '{displacement_col}' 作为Displacement数据")

        # 创建新的DataFrame，只包含Displacement数据
        new_df = pd.DataFrame({'displacement_1': df[displacement_col]}, index=df.index)

        # 过滤2016年及以后的数据
        if new_df.index.min().year < 2016:
            print(f"原始数据范围: {new_df.index.min()} 至 {new_df.index.max()}")
            new_df = new_df[new_df.index.year >= 2016]
            print(f"过滤2016年及以后的数据: {new_df.index.min()} 至 {new_df.index.max()}")

        # 生成完整Date范围并重新索引
        full_date_range = pd.date_range(start=new_df.index.min(), end=new_df.index.max(), freq='D')
        new_df = new_df.reindex(full_date_range)

        # 线性插值处理缺失值
        new_df['displacement_1'] = new_df['displacement_1'].interpolate(method='linear')

        # 提取目标数据列
        data = new_df['displacement_1']

        print(f"数据加载完成，共 {len(data)} 个数据点")
        return data

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        print("创建示例数据用于测试...")

        # 创建Date范围
        dates = pd.date_range(start='2020-01-01', periods=365, freq='D')

        # 创建模拟Displacement数据：趋势 + 季节性 + 噪声
        trend = np.linspace(0, 10, len(dates))  # 线性趋势
        seasonal = 2 * np.sin(np.linspace(0, 12*np.pi, len(dates)))  # 季节性
        noise = np.random.normal(0, 0.5, len(dates))  # 噪声

        # 组合数据
        values = trend + seasonal + noise

        # 创建DataFrame
        df = pd.DataFrame({
            'date': dates,
            'displacement_1': values
        })

        # 设置Date索引
        df.set_index('date', inplace=True)

        # 提取目标数据列
        data = df['displacement_1']

        print(f"示例数据创建完成，共 {len(data)} 个数据点")
        return data


# 计算SNR(SNR)
def calculate_snr(signal, noise):
    """计算SNR(SNR)，单位为dB(dB)"""
    signal_power = np.mean(signal ** 2)
    noise_power = np.mean(noise ** 2)
    if noise_power == 0:
        return float('inf')  # 避免除以零
    return 10 * np.log10(signal_power / noise_power)

# 2. 高级遗传算法优化卡尔曼滤波
class AdvancedGeneticKalmanFilter:
    def __init__(self, data, pop_size=120, generations=200, tournament_size=7):
        """
        初始化超级优化遗传算法优化器

        参数:
        - data: 输入数据序列
        - pop_size: 种群大小
        - generations: 迭代次数
        - tournament_size: 锦标赛选择参数
        """
        self.data = data
        self.pop_size = pop_size  # 大幅增加种群大小以提高搜索能力
        self.generations = generations  # 大幅增加迭代次数以确保收敛
        self.tournament_size = tournament_size  # 增加锦标赛选择参数以提高选择压力

        # 终极优化参数搜索范围 - 极度关注SNR提升
        self.param_ranges = {
            'Q': (1e-15, 0.1),  # 过程噪声协方差范围 - 大幅扩展范围以寻找最佳值
            'R': (1e-15, 0.1),  # 观测噪声协方差范围 - 大幅扩展范围以寻找最佳值
            'alpha': (0.5, 0.9999),  # 平滑因子 - 扩展范围以寻找最佳平滑效果
            'beta': (0.0, 0.5),  # 趋势权重 - 扩展上限以允许更多趋势影响
            'model_type_idx': (0, 2)  # 模型类型索引: 0=constant, 1=linear, 2=adaptive
        }

        # 添加超级优化参数搜索区域 - 针对极高SNR提升的特殊参数组合
        self.special_regions = [
            # 区域1: 超高平滑、超低噪声 - 适合稳定信号，追求极高SNR
            {'Q': (1e-15, 1e-10), 'R': (1e-15, 1e-10), 'alpha': (0.95, 0.9999), 'beta': (0.0, 0.05)},

            # 区域2: 高平滑、低噪声 - 适合大多数信号
            {'Q': (1e-10, 1e-7), 'R': (1e-10, 1e-7), 'alpha': (0.9, 0.99), 'beta': (0.0, 0.1)},

            # 区域3: 中等平滑、中等噪声 - 平衡方案
            {'Q': (1e-7, 1e-4), 'R': (1e-7, 1e-4), 'alpha': (0.8, 0.95), 'beta': (0.05, 0.2)},

            # 区域4: 低平滑、高噪声 - 适合快速变化信号
            {'Q': (1e-4, 1e-1), 'R': (1e-4, 1e-1), 'alpha': (0.5, 0.8), 'beta': (0.1, 0.3)},

            # 区域5: 特殊组合 - 基于历史最佳结果
            {'Q': (0.001, 0.01), 'R': (1e-6, 1e-5), 'alpha': (0.9, 0.95), 'beta': (0.2, 0.3)}
        ]

        # 模型类型映射
        self.model_types = ['constant', 'linear', 'adaptive']

        # 保存最佳个体的历史记录
        self.best_fitness_history = []
        self.best_params_history = []
        self.best_snr_history = []
        self.avg_fitness_history = []

        # 缓存以避免重复计算
        self.fitness_cache = {}

        # 最佳滤波结果
        self.best_filtered = None
        self.best_snr = 0

    def kalman_filter(self, params):
        """
        使用增强版卡尔曼滤波器处理数据

        参数:
        - params: [Q, R, alpha, beta, model_type_idx]

        返回:
        - After Filtering的数据序列
        """
        Q, R, alpha, beta, model_type_idx = params

        # 将模型类型索引转换为字符串
        model_type = self.model_types[int(np.clip(model_type_idx, 0, 2))]

        # 初始化卡尔曼滤波器
        kf = KalmanFilter(
            transition_covariance=Q,
            observation_covariance=R,
            alpha=alpha,
            beta=beta,
            model_type=model_type
        )

        # 执行滤波
        filtered_state, _ = kf.filter(self.data.values)
        filtered_series = pd.Series(filtered_state.flatten(), index=self.data.index)

        # 保存残差用于后续分析
        self.residue = kf.residue

        return filtered_series

    def fitness(self, params):
        """
        增强版适应度函数，重点优化SNR提升

        参数:
        - params: [Q, R, alpha, beta, model_type_idx]

        返回:
        - 适应度得分
        """
        # 检查参数是否有效
        for i, param_name in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
            if not np.isfinite(params[i]):
                print(f"无效参数 {param_name}: {params[i]}")
                return 0

            # 确保参数在有效范围内
            low, high = self.param_ranges[param_name]
            if params[i] < low or params[i] > high:
                print(f"参数 {param_name} 超出范围: {params[i]}, 应在 [{low}, {high}]")
                return 0

        # 检查缓存
        params_tuple = tuple(params)
        if params_tuple in self.fitness_cache:
            return self.fitness_cache[params_tuple]

        try:
            # 应用卡尔曼滤波
            filtered = self.kalman_filter(params)

            # 检查滤波结果是否包含NaN或无穷大
            if np.any(~np.isfinite(filtered.values)):
                print(f"滤波结果包含NaN或无穷大，参数: Q={params[0]:.2e}, R={params[1]:.2e}, alpha={params[2]:.2f}")
                return 0

            # 计算原始信号和After Filtering信号之间的噪声
            noise = self.data.values - filtered.values

            # 计算SNR(SNR)
            snr = calculate_snr(filtered.values, noise)

            # 检查SNR是否有效
            if not np.isfinite(snr):
                print(f"SNR计算结果无效: {snr}")
                return 0

            # 计算均方误差(MSE)
            mse = mean_squared_error(self.data, filtered)

            # 检查MSE是否有效
            if not np.isfinite(mse):
                print(f"MSE计算结果无效: {mse}")
                return 0

            # 计算平滑度（使用一阶差分的方差）
            diff_var = np.var(np.diff(filtered.values))
            if not np.isfinite(diff_var) or diff_var <= 0:
                smoothness = 0
            else:
                smoothness = 1 / (1 + diff_var)

            # 计算信号保真度（保持原始信号的重要特征）
            try:
                correlation = np.corrcoef(self.data.values, filtered.values)[0, 1]
                if not np.isfinite(correlation):
                    correlation = 0
            except:
                correlation = 0

            # 计算峰值保留率（确保重要峰值不被过度平滑）
            try:
                # 导入scipy.signal模块
                from scipy import signal as scipy_signal

                # 找出原始信号中的局部极值点
                peaks_orig = scipy_signal.find_peaks(self.data.values, prominence=np.std(self.data.values)*0.5)[0]
                valleys_orig = scipy_signal.find_peaks(-self.data.values, prominence=np.std(self.data.values)*0.5)[0]
                extrema_orig = np.sort(np.concatenate([peaks_orig, valleys_orig]))

                # 如果原始信号中有极值点
                if len(extrema_orig) > 0:
                    # 计算原始信号和滤波信号在极值位置的差异
                    diffs = np.abs(self.data.values[extrema_orig] - filtered.values[extrema_orig])
                    denoms = np.abs(self.data.values[extrema_orig]) + 1e-10
                    ratios = diffs / denoms
                    # 检查是否有无效值
                    valid_ratios = ratios[np.isfinite(ratios)]
                    if len(valid_ratios) > 0:
                        peak_preservation = 1 - np.mean(valid_ratios)
                    else:
                        peak_preservation = 0.5  # 默认值
                else:
                    peak_preservation = 1.0
            except Exception as e:
                print(f"计算峰值保留率出错: {str(e)}")
                peak_preservation = 0.5  # 默认值

            # 计算频谱保真度（保持原始信号的频谱特性）
            try:
                # 导入scipy.signal模块
                from scipy import signal as scipy_signal

                f_orig, Pxx_orig = scipy_signal.welch(self.data.values, fs=1.0, nperseg=min(256, len(self.data)//2))
                f_filt, Pxx_filt = scipy_signal.welch(filtered.values, fs=1.0, nperseg=min(256, len(self.data)//2))

                # 检查频谱计算结果是否有效
                if np.any(~np.isfinite(Pxx_orig)) or np.any(~np.isfinite(Pxx_filt)):
                    low_freq_preservation = 0.5
                    high_freq_suppression = 0.5
                else:
                    # 计算低频能量保留率（确保重要的低频信号不被过滤）
                    low_freq_idx = f_orig <= 0.1  # 低频部分
                    if np.sum(Pxx_orig[low_freq_idx]) > 0:
                        low_freq_preservation = np.sum(Pxx_filt[low_freq_idx]) / np.sum(Pxx_orig[low_freq_idx])
                        low_freq_preservation = min(1.0, max(0.0, low_freq_preservation))  # 限制在0-1之间
                    else:
                        low_freq_preservation = 1.0

                    # 计算高频噪声抑制率（确保高频噪声被有效抑制）
                    high_freq_idx = f_orig > 0.3  # 高频部分
                    if np.sum(Pxx_orig[high_freq_idx]) > 0:
                        high_freq_suppression = 1 - np.sum(Pxx_filt[high_freq_idx]) / np.sum(Pxx_orig[high_freq_idx])
                        high_freq_suppression = min(1.0, max(0.0, high_freq_suppression))  # 限制在0-1之间
                    else:
                        high_freq_suppression = 1.0
            except Exception as e:
                print(f"计算频谱特性出错: {str(e)}")
                low_freq_preservation = 0.5
                high_freq_suppression = 0.5

            # 超级优化评分：极大提高SNR的权重，确保SNR有显著提升
            # 目标是最大化SNR提升

            # 估计原始信号的SNR
            try:
                from scipy import signal
                # 使用频谱分析估计原始信号的噪声水平
                f_orig, Pxx_orig = signal.welch(self.data.values, fs=1.0, nperseg=min(256, len(self.data)//2))

                # 假设高频部分主要是噪声
                high_freq_idx = f_orig > 0.3
                noise_power_orig = np.mean(Pxx_orig[high_freq_idx]) if np.any(high_freq_idx) else 0.1
                signal_power_orig = np.mean(Pxx_orig) - noise_power_orig

                # 估计原始信号的SNR
                original_snr = 10 * np.log10(signal_power_orig / noise_power_orig) if noise_power_orig > 0 else 10
            except:
                # 如果频谱分析失败，使用默认值
                original_snr = 10  # 假设原始信号的SNR约为10dB

            # 计算SNR提升
            snr_improvement = snr - original_snr

            # 使用非线性函数放大SNR提升的影响 - 直接用于评分
            # snr_score = 0.7 * (1 - np.exp(-0.2 * max(0, snr_improvement)))  # 使用指数函数放大小的提升

            # 终极评分：极度提高SNR提升的权重
            score = ((0.85 * min(snr_improvement, 40)) + (0.05 * correlation) + (0.03 * peak_preservation)
                    + (0.03 * low_freq_preservation) + (0.02 * high_freq_suppression) + (0.02 * smoothness))

            # 添加超级惩罚项：如果SNR提升不显著（小于15dB），则大幅降低得分
            if snr_improvement < 15:
                penalty = 0.9 * (1 - snr_improvement / 15)
                score = score * (1 - penalty)

            # 添加超级奖励项：如果SNR提升超过20dB，则大幅加分
            if snr_improvement > 20:
                bonus = 0.5 * (snr_improvement - 20) / 10  # 每超过20dB的10dB增加50%的分数
                score = score * (1 + min(bonus, 1.0))  # 最多增加100%

            # 额外奖励：如果SNR提升超过35dB，给予特别奖励
            if snr_improvement > 35:
                score = score * 1.5  # 额外增加50%

            # 确保得分有效
            if not np.isfinite(score):
                print(f"计算得分无效: {score}")
                return 0

            # 更新最佳滤波结果
            if np.isfinite(snr) and snr > self.best_snr:
                self.best_snr = snr
                self.best_filtered = filtered

            # 缓存结果
            self.fitness_cache[params_tuple] = max(score, 0)

            return max(score, 0)  # 确保适应度非负
        except Exception as e:
            print(f"适应度计算错误: {str(e)}")
            return 0

    def tournament_selection(self, population, scores):
        """
        锦标赛选择

        参数:
        - population: 当前种群
        - scores: 适应度得分

        返回:
        - 选择的父代
        """
        selected = []
        for _ in range(self.pop_size // 2):
            # 随机选择tournament_size个个体
            candidates_idx = np.random.choice(len(population), self.tournament_size, replace=False)
            # 选择其中适应度最高的个体
            best_idx = candidates_idx[np.argmax(scores[candidates_idx])]
            selected.append(population[best_idx])
        return np.array(selected)

    def evolve(self):
        """
        高级进化算法

        返回:
        - 最佳参数 [Q, R, alpha, beta, model_type]
        """
        # 使用超级优化的混合采样策略初始化种群
        print("使用超级优化的混合采样策略初始化种群...")

        # 初始化种群数组
        population = np.zeros((self.pop_size, 5))

        try:
            from scipy.stats import qmc

            # 分配种群比例
            general_ratio = 0.4  # 一般搜索空间的比例
            special_ratio = 0.5  # 特殊区域的比例
            random_ratio = 0.1   # 完全随机的比例

            # 计算各部分的个体数量
            general_count = int(self.pop_size * general_ratio)
            special_count = int(self.pop_size * special_ratio)
            random_count = self.pop_size - general_count - special_count

            # 1. 一般搜索空间 - 使用拉丁超立方采样
            if general_count > 0:
                # 创建拉丁超立方采样器
                sampler = qmc.LatinHypercube(d=5)

                # 生成样本
                samples = sampler.random(n=general_count)

                # 将样本缩放到参数范围
                for i, param in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                    low, high = self.param_ranges[param]
                    if param in ['Q', 'R']:
                        # 对数均匀分布
                        log_low, log_high = np.log10(low), np.log10(high)
                        population[:general_count, i] = 10 ** (log_low + samples[:, i] * (log_high - log_low))
                    elif param == 'model_type_idx':
                        # 离散均匀分布
                        population[:general_count, i] = np.floor(low + samples[:, i] * (high - low + 1))
                        population[:general_count, i] = np.clip(population[:general_count, i], low, high)
                    else:
                        # 连续均匀分布
                        population[:general_count, i] = low + samples[:, i] * (high - low)

            # 2. 特殊区域 - 针对SNR优化的参数组合
            if special_count > 0:
                # 为每个特殊区域分配个体
                region_counts = []
                remaining = special_count
                for i in range(len(self.special_regions) - 1):
                    count = remaining // (len(self.special_regions) - i)
                    region_counts.append(count)
                    remaining -= count
                region_counts.append(remaining)  # 最后一个区域

                start_idx = general_count
                for r, region in enumerate(self.special_regions):
                    count = region_counts[r]
                    if count <= 0:
                        continue

                    # 为当前区域创建拉丁超立方采样
                    region_sampler = qmc.LatinHypercube(d=5)
                    region_samples = region_sampler.random(n=count)

                    # 填充参数
                    for i, param in enumerate(['Q', 'R', 'alpha', 'beta']):
                        if param in region:
                            low, high = region[param]
                        else:
                            low, high = self.param_ranges[param]

                        if param in ['Q', 'R']:
                            # 对数均匀分布
                            log_low, log_high = np.log10(low), np.log10(high)
                            population[start_idx:start_idx+count, i] = 10 ** (log_low + region_samples[:, i] * (log_high - log_low))
                        else:
                            # 连续均匀分布
                            population[start_idx:start_idx+count, i] = low + region_samples[:, i] * (high - low)

                    # 模型类型 - 优先使用adaptive模型
                    if r == 0:  # 第一个区域使用constant
                        population[start_idx:start_idx+count, 4] = 0
                    elif r == 1:  # 第二个区域使用linear
                        population[start_idx:start_idx+count, 4] = 1
                    else:  # 其他区域使用adaptive
                        population[start_idx:start_idx+count, 4] = 2

                    start_idx += count

            # 3. 完全随机部分 - 增加多样性
            if random_count > 0:
                for i, param in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                    low, high = self.param_ranges[param]
                    if param in ['Q', 'R']:
                        # 对数均匀分布
                        log_low, log_high = np.log10(low), np.log10(high)
                        population[-random_count:, i] = 10 ** (np.random.uniform(log_low, log_high, random_count))
                    elif param == 'model_type_idx':
                        # 离散均匀分布
                        population[-random_count:, i] = np.random.randint(low, high + 1, random_count)
                    else:
                        # 连续均匀分布
                        population[-random_count:, i] = np.random.uniform(low, high, random_count)

            print(f"成功初始化种群: {general_count}个一般搜索空间, {special_count}个特殊区域, {random_count}个随机个体")

        except Exception as e:
            print(f"高级混合采样初始化失败: {str(e)}")
            print("使用备用随机初始化...")

            # 备用方案：使用均匀随机初始化
            for i, param in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                low, high = self.param_ranges[param]
                if param in ['Q', 'R']:
                    # 对数均匀分布
                    log_low, log_high = np.log10(low), np.log10(high)
                    population[:, i] = 10 ** (np.random.uniform(log_low, log_high, self.pop_size))
                elif param == 'model_type_idx':
                    # 离散均匀分布
                    population[:, i] = np.random.randint(low, high + 1, self.pop_size)
                else:
                    # 连续均匀分布
                    population[:, i] = np.random.uniform(low, high, self.pop_size)

        # 进化过程
        print("开始高级遗传算法优化...")
        start_time = time.time()

        for gen in range(self.generations):
            gen_start_time = time.time()

            # 计算适应度
            scores = np.array([self.fitness(ind) for ind in population])

            # 记录当前代最佳个体
            best_idx = np.argmax(scores)
            best_score = scores[best_idx]
            best_params = population[best_idx].copy()

            # 记录历史
            self.best_fitness_history.append(best_score)
            self.best_params_history.append(best_params)
            self.avg_fitness_history.append(np.mean(scores))

            # 计算最佳个体的SNR
            best_filtered = self.kalman_filter(best_params)
            noise = self.data.values - best_filtered.values
            best_snr = calculate_snr(best_filtered.values, noise)
            self.best_snr_history.append(best_snr)

            # 每10代打印进度
            if gen % 10 == 0 or gen == self.generations - 1:
                elapsed = time.time() - start_time
                gen_time = time.time() - gen_start_time
                remaining = (self.generations - gen - 1) * gen_time

                print(f"代数: {gen+1}/{self.generations} | "
                      f"最佳适应度: {best_score:.4f} | "
                      f"最佳SNR: {best_snr:.2f} dB | "
                      f"耗时: {elapsed:.1f}s | "
                      f"预计剩余: {remaining:.1f}s")

            # 如果达到最后一代，跳出循环
            if gen == self.generations - 1:
                break

            # 锦标赛选择父代
            parents = self.tournament_selection(population, scores)

            # 生成子代
            children = []
            while len(children) < self.pop_size - len(parents):
                # 选择两个父代
                p1, p2 = parents[np.random.choice(len(parents), 2, replace=False)]

                # 确保父代参数在有效范围内
                for i, param_name in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                    low, high = self.param_ranges[param_name]
                    p1[i] = np.clip(p1[i], low, high)
                    p2[i] = np.clip(p2[i], low, high)

                # 模拟二进制交叉(SBX)
                eta = 15  # 分布指数
                u = np.random.random(5)

                # SBX交叉
                beta = np.zeros(5)
                for i in range(5):
                    if u[i] <= 0.5:
                        beta[i] = (2 * u[i]) ** (1 / (eta + 1))
                    else:
                        beta[i] = (1 / (2 * (1 - u[i]))) ** (1 / (eta + 1))

                # 生成子代
                c1 = np.zeros(5)
                c2 = np.zeros(5)

                # 对每个参数分别进行交叉，确保在有效范围内
                for i, param_name in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                    low, high = self.param_ranges[param_name]

                    # 计算子代值
                    c1[i] = 0.5 * ((1 + beta[i]) * p1[i] + (1 - beta[i]) * p2[i])
                    c2[i] = 0.5 * ((1 - beta[i]) * p1[i] + (1 + beta[i]) * p2[i])

                    # 确保在有效范围内
                    c1[i] = np.clip(c1[i], low, high)
                    c2[i] = np.clip(c2[i], low, high)

                # 多项式变异
                for child in [c1, c2]:
                    if np.random.rand() < 0.3:  # 变异概率
                        for i in range(5):
                            if np.random.rand() < 0.5:  # 每个参数的变异概率
                                param_name = ['Q', 'R', 'alpha', 'beta', 'model_type_idx'][i]
                                low, high = self.param_ranges[param_name]

                                # 自适应变异：随着代数增加，变异幅度减小
                                decay_rate = 1 - gen / self.generations

                                if param_name in ['Q', 'R']:
                                    # 对数尺度变异
                                    log_val = np.log10(max(child[i], low))  # 确保取对数前值为正
                                    log_low, log_high = np.log10(low), np.log10(high)
                                    sigma = decay_rate * 0.1 * (log_high - log_low)
                                    log_val += np.random.normal(0, sigma)
                                    child[i] = 10 ** np.clip(log_val, log_low, log_high)
                                else:
                                    # 线性尺度变异
                                    sigma = decay_rate * 0.1 * (high - low)
                                    child[i] += np.random.normal(0, sigma)
                                    child[i] = np.clip(child[i], low, high)

                # 最终检查确保所有参数都在有效范围内
                for child in [c1, c2]:
                    for i, param_name in enumerate(['Q', 'R', 'alpha', 'beta', 'model_type_idx']):
                        low, high = self.param_ranges[param_name]
                        child[i] = np.clip(child[i], low, high)

                # 添加到子代列表
                children.extend([c1, c2])

            # 截断子代列表以匹配所需数量
            children = children[:self.pop_size - len(parents)]

            # 精英保留策略：保留最佳个体
            elite_size = max(1, int(self.pop_size * 0.1))  # 10%的精英保留率
            elite_indices = np.argsort(scores)[-elite_size:]
            elites = population[elite_indices]

            # 更新种群
            population = np.vstack([elites, parents[:self.pop_size-elite_size-len(children)], children])

        # 找出历史上SNR最高的前5个参数组合
        top_n = 5  # 保存前5个最佳结果

        # 根据SNR对历史记录进行排序
        snr_indices = np.argsort(self.best_snr_history)[::-1]  # 降序排列
        top_snr_indices = snr_indices[:top_n]

        # 打印前5个最佳结果
        print("\n前5个最佳SNR结果:")
        for i, idx in enumerate(top_snr_indices):
            params = self.best_params_history[idx]
            snr = self.best_snr_history[idx]
            fitness = self.best_fitness_history[idx]

            # 转换模型类型索引为字符串
            model_type_idx = int(np.clip(params[4], 0, 2))
            model_type = self.model_types[model_type_idx]

            print(f"\n结果 #{i+1} - SNR: {snr:.2f} dB, 适应度: {fitness:.6f}")
            print(f"过程噪声协方差 (Q): {params[0]:.8f}")
            print(f"观测噪声协方差 (R): {params[1]:.8f}")
            print(f"平滑因子 (alpha): {params[2]:.4f}")
            print(f"趋势权重 (beta): {params[3]:.4f}")
            print(f"模型类型: {model_type}")

        # 选择SNR最高的参数作为最终结果
        best_idx = top_snr_indices[0]
        best_params = self.best_params_history[best_idx]

        # 转换模型类型索引为字符串
        model_type_idx = int(np.clip(best_params[4], 0, 2))
        model_type = self.model_types[model_type_idx]

        print(f"\n选择SNR最高的结果作为最终参数 (SNR: {self.best_snr_history[best_idx]:.2f} dB)")

        # 返回最佳参数 [Q, R, alpha, beta, model_type]
        return [best_params[0], best_params[1], best_params[2], best_params[3], model_type]


# 添加频谱分析函数
def analyze_spectrum(data, title="频谱分析", fs=1.0):
    """对信号进行频谱分析并可视化"""
    # 导入scipy.signal模块
    from scipy import signal as scipy_signal

    # 计算功率谱密度
    f, Pxx = scipy_signal.welch(data, fs=fs, nperseg=min(256, len(data)//2))

    plt.figure(figsize=(10, 6))
    plt.semilogy(f, Pxx)
    plt.title(title)
    plt.xlabel('频率')
    plt.ylabel('功率谱密度')
    plt.grid(True)
    plt.tight_layout()
    return f, Pxx

# 计算信号能量分布
def calculate_energy_distribution(psd, freq):
    """计算信号在不同频段的能量分布百分比"""
    total_energy = np.sum(psd)

    # 定义频段（可根据实际信号特性调整）
    low_freq_idx = freq <= 0.1  # 低频
    mid_freq_idx = (freq > 0.1) & (freq <= 0.3)  # 中频
    high_freq_idx = freq > 0.3  # 高频

    # 计算各频段能量占比
    low_energy_pct = np.sum(psd[low_freq_idx]) / total_energy * 100
    mid_energy_pct = np.sum(psd[mid_freq_idx]) / total_energy * 100
    high_energy_pct = np.sum(psd[high_freq_idx]) / total_energy * 100

    return {
        "低频占比(%)": low_energy_pct,
        "中频占比(%)": mid_energy_pct,
        "高频占比(%)": high_energy_pct
    }

# 主程序流程
if __name__ == "__main__":
    # 创建输出目录
    output_dir = './output/kalman_filter'
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")

    # 载入并预处理数据
    try:
        # 尝试加载第一列Original Data.xlsx
        data_file = '第一列Original Data.xlsx'
        if os.path.exists(data_file):
            print(f"Loading Original Data: {data_file}")
            original_data = load_data(data_file)
        else:
            # 如果文件不存在，使用默认数据
            print(f"Data file not found: {data_file}, using default data...")
            original_data = load_data()

        print(f"数据加载成功，共 {len(original_data)} 个数据点")
    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        exit()

    # 终极优化遗传算法优化参数
    print("开始优化卡尔曼滤波参数...")
    optimizer = AdvancedGeneticKalmanFilter(
        original_data,
        pop_size=200,  # 大幅增加种群大小
        generations=300,  # 大幅增加迭代次数
        tournament_size=10  # 增加锦标赛选择压力
    )

    # 执行优化
    best_params = optimizer.evolve()
    best_Q, best_R, best_alpha, best_beta, best_model_type = best_params

    print("\n优化完成！最佳参数:")
    print(f"过程噪声协方差 (Q): {best_Q:.8f}")
    print(f"观测噪声协方差 (R): {best_R:.8f}")
    print(f"平滑因子 (alpha): {best_alpha:.4f}")
    print(f"趋势权重 (beta): {best_beta:.4f}")
    print(f"模型类型: {best_model_type}")

    # 应用最佳参数的卡尔曼滤波
    kf = KalmanFilter(
        transition_covariance=best_Q,
        observation_covariance=best_R,
        alpha=best_alpha,
        beta=best_beta,
        model_type=best_model_type
    )
    filtered_state, _ = kf.filter(original_data.values)
    filtered_data = pd.Series(filtered_state.flatten(), index=original_data.index)

    # 计算噪声和SNR
    noise = original_data.values - filtered_data.values

    # 估计原始信号的SNR
    # 使用频谱分析估计原始信号的噪声水平
    from scipy import signal as scipy_signal
    f_orig, Pxx_orig = scipy_signal.welch(original_data.values, fs=1.0, nperseg=min(256, len(original_data)//2))

    # 假设高频部分主要是噪声
    high_freq_idx = f_orig > 0.3
    noise_power = np.mean(Pxx_orig[high_freq_idx]) if np.any(high_freq_idx) else 0.1
    signal_power = np.mean(Pxx_orig) - noise_power

    # 估计原始信号的SNR
    snr_before = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else 10

    # 计算After Filtering的SNR
    snr_after = calculate_snr(filtered_data.values, noise)

    # 计算SNR提升
    snr_improvement = snr_after - snr_before

    print(f"\nSNR分析:")
    print(f"原始信号估计SNR: {snr_before:.2f} dB")
    print(f"After FilteringSNR (SNR): {snr_after:.2f} dB")
    print(f"SNR提升: {snr_improvement:.2f} dB")

    # 频谱分析
    print("\n进行频谱分析...")
    f_orig, Pxx_orig = analyze_spectrum(original_data.values, title="原始信号频谱")
    f_filt, Pxx_filt = analyze_spectrum(filtered_data.values, title="After Filtering信号频谱")

    # 计算能量分布
    orig_energy = calculate_energy_distribution(Pxx_orig, f_orig)
    filt_energy = calculate_energy_distribution(Pxx_filt, f_filt)

    print("\n能量分布分析:")
    print("原始信号:")
    for k, v in orig_energy.items():
        print(f"  {k}: {v:.2f}%")

    print("After Filtering信号:")
    for k, v in filt_energy.items():
        print(f"  {k}: {v:.2f}%")

    # 结果可视化 - 高级版（突出显示SNR提升）
    plt.figure(figsize=(16, 12))

    # 1. 时域对比图（主图）
    plt.subplot(2, 2, 1)
    plt.plot(original_data.index, original_data,
             label='Original Data', linewidth=1, alpha=0.7, color='#1f77b4')
    plt.plot(filtered_data.index, filtered_data,
             label='Filtered Data', linewidth=1.5, color='#ff7f0e')
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Displacement Value', fontsize=12)
    plt.title(f'Genetic Algorithm Optimized Kalman Filter Denoising Comparison', fontsize=14)
    plt.grid(alpha=0.3)
    plt.legend(loc='best')

    # 2. SNR提升图（突出显示）
    plt.subplot(2, 2, 2)
    # Create bar chart comparing SNR before and after denoising
    bars = plt.bar(['Before Filtering', 'After Filtering'], [snr_before, snr_after],
                  color=['#1f77b4', '#ff7f0e'], alpha=0.8)
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.2f} dB', ha='center', va='bottom', fontsize=12)
    # Add improvement arrow and text
    plt.annotate(f'Improvement: {snr_improvement:.2f} dB',
                xy=(1, snr_before + (snr_improvement)/2),
                xytext=(1.5, snr_before + (snr_improvement)/2),
                arrowprops=dict(facecolor='green', shrink=0.05, width=2),
                fontsize=12, color='green', weight='bold')
    plt.ylabel('SNR (dB)', fontsize=12)
    plt.title('Kalman Filter SNR Improvement Effect', fontsize=14, color='darkgreen')
    plt.grid(axis='y', alpha=0.3)

    # 设置y轴上限，确保箭头和文本可见
    plt.ylim(0, max(snr_before, snr_after) * 1.3)

    # 3. 噪声分析图
    plt.subplot(2, 2, 3)
    plt.plot(original_data.index, noise, label='Removed Noise', color='#d62728', alpha=0.7)
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.3)
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Noise Amplitude', fontsize=12)
    plt.title('Removed Noise Analysis', fontsize=14)
    plt.grid(alpha=0.3)
    plt.legend(loc='best')

    # Add noise statistics
    noise_std = np.std(noise)
    noise_mean = np.mean(noise)
    textstr = f'Noise Std Dev: {noise_std:.4f}\nNoise Mean: {noise_mean:.4f}'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    plt.text(0.05, 0.95, textstr, transform=plt.gca().transAxes, fontsize=10,
            verticalalignment='top', bbox=props)

    # 4. 局部放大图（显示细节Improvement）
    plt.subplot(2, 2, 4)
    # 选择数据的中间部分进行放大显示
    mid_point = len(original_data) // 2
    window = len(original_data) // 8  # 增大窗口以显示更多细节
    start_idx = mid_point - window
    end_idx = mid_point + window

    plt.plot(original_data.index[start_idx:end_idx],
             original_data.values[start_idx:end_idx],
             label='Original Data', linewidth=1.5, alpha=0.7, color='#1f77b4')
    plt.plot(filtered_data.index[start_idx:end_idx],
             filtered_data.values[start_idx:end_idx],
             label='Filtered Data', linewidth=2, color='#ff7f0e')
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Displacement Value', fontsize=12)
    plt.title('Zoomed View - Detail Comparison', fontsize=14)
    plt.grid(alpha=0.3)
    plt.legend(loc='best')

    # Add parameter information
    textstr = (f'Optimized Parameters:\nQ={best_Q:.2e}, R={best_R:.2e}\n'
              f'α={best_alpha:.2f}, β={best_beta:.2f}\n'
              f'Model: {best_model_type}')
    props = dict(boxstyle='round', facecolor='lightblue', alpha=0.5)
    plt.text(0.05, 0.05, textstr, transform=plt.gca().transAxes, fontsize=10,
            verticalalignment='bottom', bbox=props)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'Kalman_Filtering_Results_{timestamp}.png'),
                dpi=300, bbox_inches='tight')

    # 保存增强版频谱分析图
    plt.figure(figsize=(16, 8))

    # 1. 频谱对比图
    plt.subplot(2, 2, 1)
    plt.semilogy(f_orig, Pxx_orig, color='#1f77b4', label='Original Signal')
    plt.semilogy(f_filt, Pxx_filt, color='#ff7f0e', label='Filtered Signal')
    plt.title('Spectrum Comparison Analysis', fontsize=14)
    plt.xlabel('Frequency', fontsize=12)
    plt.ylabel('Power Spectral Density (Log Scale)', fontsize=12)
    plt.grid(True, alpha=0.5)
    plt.legend()

    # 2. High frequency noise suppression effect
    plt.subplot(2, 2, 2)
    # Calculate high frequency energy ratio
    high_freq_idx = f_orig > 0.3  # High frequency part
    if np.any(high_freq_idx):
        orig_high_energy = np.sum(Pxx_orig[high_freq_idx])
        filt_high_energy = np.sum(Pxx_filt[high_freq_idx])
        high_freq_reduction = (orig_high_energy - filt_high_energy) / orig_high_energy * 100

        bars = plt.bar(['Original Signal', 'Filtered Signal'],
                      [orig_high_energy, filt_high_energy],
                      color=['#1f77b4', '#ff7f0e'], alpha=0.8)

        # Add value labels
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height * 1.05,
                    f'{height:.2e}', ha='center', va='bottom', fontsize=10)

        # Add reduction percentage
        plt.annotate(f'Reduction: {high_freq_reduction:.1f}%',
                    xy=(1, filt_high_energy + (orig_high_energy - filt_high_energy)/2),
                    xytext=(1.5, filt_high_energy + (orig_high_energy - filt_high_energy)/2),
                    arrowprops=dict(facecolor='green', shrink=0.05, width=1.5),
                    fontsize=12, color='green', weight='bold')

        plt.title('High Frequency Noise Suppression Effect', fontsize=14)
        plt.ylabel('High Frequency Energy', fontsize=12)
        plt.grid(axis='y', alpha=0.5)
    else:
        plt.text(0.5, 0.5, 'No high frequency components', ha='center', va='center', fontsize=14)
        plt.title('High Frequency Noise Suppression Effect', fontsize=14)

    # 3. Original signal spectrum
    plt.subplot(2, 2, 3)
    plt.semilogy(f_orig, Pxx_orig, color='#1f77b4')
    # Mark low and high frequency regions
    low_freq_idx = f_orig <= 0.1
    mid_freq_idx = (f_orig > 0.1) & (f_orig <= 0.3)
    high_freq_idx = f_orig > 0.3

    if np.any(low_freq_idx):
        plt.fill_between(f_orig[low_freq_idx], Pxx_orig[low_freq_idx],
                        alpha=0.3, color='green', label='Low Frequency Region')
    if np.any(mid_freq_idx):
        plt.fill_between(f_orig[mid_freq_idx], Pxx_orig[mid_freq_idx],
                        alpha=0.3, color='yellow', label='Mid Frequency Region')
    if np.any(high_freq_idx):
        plt.fill_between(f_orig[high_freq_idx], Pxx_orig[high_freq_idx],
                        alpha=0.3, color='red', label='High Frequency Region')

    plt.title('Original Signal Spectrum', fontsize=14)
    plt.xlabel('Frequency', fontsize=12)
    plt.ylabel('Power Spectral Density', fontsize=12)
    plt.grid(True, alpha=0.5)
    plt.legend(loc='best')

    # 4. Filtered signal spectrum
    plt.subplot(2, 2, 4)
    plt.semilogy(f_filt, Pxx_filt, color='#ff7f0e')
    # Mark low and high frequency regions
    if np.any(low_freq_idx):
        plt.fill_between(f_filt[low_freq_idx], Pxx_filt[low_freq_idx],
                        alpha=0.3, color='green', label='Low Frequency Region')
    if np.any(mid_freq_idx):
        plt.fill_between(f_filt[mid_freq_idx], Pxx_filt[mid_freq_idx],
                        alpha=0.3, color='yellow', label='Mid Frequency Region')
    if np.any(high_freq_idx):
        plt.fill_between(f_filt[high_freq_idx], Pxx_filt[high_freq_idx],
                        alpha=0.3, color='red', label='High Frequency Region')

    plt.title('Filtered Signal Spectrum', fontsize=14)
    plt.xlabel('Frequency', fontsize=12)
    plt.ylabel('Power Spectral Density', fontsize=12)
    plt.grid(True, alpha=0.5)
    plt.legend(loc='best')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'Spectrum_Analysis_{timestamp}.png'),
                dpi=300, bbox_inches='tight')

    # 保存增强版优化过程图
    plt.figure(figsize=(16, 8))

    # 1. 适应度历史
    plt.subplot(2, 2, 1)
    generations = range(1, len(optimizer.best_fitness_history) + 1)
    plt.plot(generations, optimizer.best_fitness_history, 'b-', label='最佳适应度', linewidth=2)
    plt.plot(generations, optimizer.avg_fitness_history, 'g--', label='平均适应度', alpha=0.7)
    plt.title('遗传算法优化过程 - 适应度历史', fontsize=14)
    plt.xlabel('代数', fontsize=12)
    plt.ylabel('适应度', fontsize=12)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    # 2. SNR历史
    plt.subplot(2, 2, 2)
    plt.plot(generations, optimizer.best_snr_history, 'r-', label='最佳SNR', linewidth=2)
    plt.axhline(y=snr_before, color='k', linestyle='--', label=f'原始SNR: {snr_before:.2f} dB', alpha=0.7)
    plt.title('遗传算法优化过程 - SNR历史', fontsize=14)
    plt.xlabel('代数', fontsize=12)
    plt.ylabel('SNR (dB)', fontsize=12)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    # 3. 参数收敛历史 - Q和R (对数尺度)
    plt.subplot(2, 2, 3)
    q_history = [params[0] for params in optimizer.best_params_history]
    r_history = [params[1] for params in optimizer.best_params_history]

    plt.semilogy(generations, q_history, 'b-', label='过程噪声 (Q)', linewidth=1.5)
    plt.semilogy(generations, r_history, 'r-', label='观测噪声 (R)', linewidth=1.5)
    plt.title('参数收敛历史 - Q和R', fontsize=14)
    plt.xlabel('代数', fontsize=12)
    plt.ylabel('参数值 (对数尺度)', fontsize=12)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    # 4. 参数收敛历史 - alpha和beta
    plt.subplot(2, 2, 4)
    alpha_history = [params[2] for params in optimizer.best_params_history]
    beta_history = [params[3] for params in optimizer.best_params_history]

    plt.plot(generations, alpha_history, 'g-', label='平滑因子 (α)', linewidth=1.5)
    plt.plot(generations, beta_history, 'm-', label='趋势权重 (β)', linewidth=1.5)
    plt.title('参数收敛历史 - α和β', fontsize=14)
    plt.xlabel('代数', fontsize=12)
    plt.ylabel('参数值', fontsize=12)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'Optimization_Process_{timestamp}.png'),
                dpi=300, bbox_inches='tight')

    # 保存数据
    result_df = pd.DataFrame({
        'Date': original_data.index,
        'Original Data': original_data.values,
        '去噪数据': filtered_data.values,
        '噪声': noise
    })

    # 添加SNR和参数信息
    info_df = pd.DataFrame({
        '参数': ['过程噪声协方差(Q)', '观测噪声协方差(R)', '平滑因子(alpha)', '趋势权重(beta)',
                '模型类型', '原始信号SNR(dB)', 'After FilteringSNR(dB)', 'SNR提升(dB)'],
        '值': [best_Q, best_R, best_alpha, best_beta, best_model_type,
              snr_before, snr_after, snr_improvement]
    })

    # 添加频谱能量分布
    energy_df = pd.DataFrame({
        '频段': list(orig_energy.keys()),
        '原始信号(%)': list(orig_energy.values()),
        'After Filtering信号(%)': list(filt_energy.values())
    })

    # 保存到Excel
    excel_path = os.path.join(output_dir, f'去噪结果数据_{timestamp}.xlsx')
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='去噪结果', index=False)
        info_df.to_excel(writer, sheet_name='参数信息', index=False)
        energy_df.to_excel(writer, sheet_name='频谱分析', index=False)

    # 误差分析
    mse = mean_squared_error(original_data, filtered_data)
    mae = mean_absolute_error(original_data, filtered_data)
    r2 = r2_score(original_data, filtered_data)

    print("\n误差分析结果：")
    print(f"均方误差 (MSE): {mse:.10f}")
    print(f"平均绝对误差 (MAE): {mae:.10f}")
    print(f"决定系数 (R²): {r2:.10f}")
    print(f"SNR (SNR): {snr_after:.2f} dB")

    print(f"\n结果已保存至：{output_dir}")