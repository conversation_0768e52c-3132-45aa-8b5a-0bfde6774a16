#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复标签脚本
将中文标签替换为英文标签，确保图表正确显示
"""

import os
import re

def fix_main_ceemdan():
    """修复main_CEEMDAN.py中的标签"""
    file_path = 'main_CEEMDAN.py'
    
    # 备份原始文件
    backup_path = file_path + '.bak'
    if not os.path.exists(backup_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建备份文件: {backup_path}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换中文标签为英文标签
    replacements = [
        ('原始数据', 'Original Data'),
        ('原始时间序列', 'Original Time Series'),
        ('残差', 'Residual'),
        ('残差分量', 'Residual Component'),
        ('长期趋势', 'Long-term Trend'),
        ('日期', 'Date'),
        ('CEEMDAN模态分解结果', 'CEEMDAN Mode Decomposition Results'),
        ('残差时间序列与趋势', 'Residual Time Series and Trend'),
        ('振幅', 'Amplitude'),
        ('残差自相关函数', 'Residual Autocorrelation Function'),
        ('残差偏自相关函数', 'Residual Partial Autocorrelation Function'),
        ('残差分布直方图', 'Residual Distribution Histogram'),
        ('数值', 'Value'),
        ('频率', 'Frequency'),
        ('残差散点图', 'Residual Scatter Plot'),
        ('索引', 'Index'),
        ('无明显周期', 'No obvious period'),
        ('天', 'days'),
        ('周期', 'Period')
    ]
    
    for cn, en in replacements:
        content = content.replace(cn, en)
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复 {file_path} 中的标签")

def fix_main_kalman():
    """修复main_KalmanFilter.py中的标签"""
    file_path = 'main_KalmanFilter.py'
    
    # 备份原始文件
    backup_path = file_path + '.bak'
    if not os.path.exists(backup_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建备份文件: {backup_path}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换中文标签为英文标签
    replacements = [
        ('原始数据', 'Original Data'),
        ('滤波后数据', 'Filtered Data'),
        ('卡尔曼滤波结果', 'Kalman Filter Results'),
        ('日期', 'Date'),
        ('位移', 'Displacement'),
        ('信噪比', 'SNR'),
        ('分贝', 'dB'),
        ('滤波前', 'Before Filtering'),
        ('滤波后', 'After Filtering'),
        ('改进', 'Improvement')
    ]
    
    for cn, en in replacements:
        content = content.replace(cn, en)
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复 {file_path} 中的标签")

def fix_bilstm():
    """修复BiLSTM+TA（无bug）.py中的标签"""
    file_path = 'BiLSTM+TA（无bug）.py'
    
    # 备份原始文件
    backup_path = file_path + '.bak'
    if not os.path.exists(backup_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建备份文件: {backup_path}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换中文标签为英文标签
    replacements = [
        ('实际值', 'Actual'),
        ('预测值', 'Predicted'),
        ('位移预测结果', 'Displacement Prediction Results'),
        ('日期', 'Date'),
        ('位移', 'Displacement'),
        ('预测结果对比', 'Prediction Results Comparison'),
        ('残差', 'Residuals'),
        ('预测残差', 'Prediction Residuals'),
        ('对称相对误差', 'Symmetric Relative Error'),
        ('预测误差', 'Prediction Error'),
        ('训练损失', 'Training Loss'),
        ('验证损失', 'Validation Loss'),
        ('模型训练损失曲线', 'Model Training Loss Curve'),
        ('训练轮次', 'Epochs'),
        ('损失值', 'Loss'),
        ('平均绝对误差', 'Mean Absolute Error'),
        ('训练MAE', 'Training MAE'),
        ('验证MAE', 'Validation MAE')
    ]
    
    for cn, en in replacements:
        content = content.replace(cn, en)
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复 {file_path} 中的标签")

def main():
    """主函数"""
    print("开始修复标签...")
    
    # 修复三个主要文件中的标签
    fix_main_ceemdan()
    fix_main_kalman()
    fix_bilstm()
    
    print("标签修复完成！")

if __name__ == "__main__":
    main()
