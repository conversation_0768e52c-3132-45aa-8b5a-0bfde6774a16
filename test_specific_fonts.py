import matplotlib.font_manager as fm
import matplotlib.pyplot as plt
import numpy as np
import os

# 测试特定的中文字体
chinese_fonts = [
    'SimHei', 
    'SimSun', 
    'NSimSun', 
    'FangSong', 
    'KaiTi', 
    'Microsoft YaHei', 
    'Microsoft JhengHei',
    'Source Han Sans CN', 
    'Source Han Serif CN',
    'WenQuanYi Micro Hei', 
    'WenQuanYi Zen Hei',
    'Noto Sans CJK SC', 
    'Noto Sans CJK TC', 
    'Noto Sans CJK JP',
    'Noto Serif CJK SC', 
    'Noto Serif CJK TC', 
    'Noto Serif CJK JP',
    'AR PL UMing CN', 
    'AR PL UKai CN',
    'DejaVu Sans', 
    'DejaVu Serif',
    'Noto Sans Gurmukhi'  # 您提到的第3个字体
]

# 获取所有可用字体
available_fonts = [f.name for f in fm.fontManager.ttflist]
print(f"系统中共有 {len(available_fonts)} 个字体")

# 检查哪些中文字体可用
available_chinese_fonts = []
for font in chinese_fonts:
    if font in available_fonts:
        available_chinese_fonts.append(font)
        print(f"字体可用: {font}")
    else:
        print(f"字体不可用: {font}")

# 如果没有找到任何中文字体，尝试查找包含特定关键词的字体
if not available_chinese_fonts:
    keywords = ['han', 'hei', 'kai', 'ming', 'song', 'gothic', 'mincho', 
                'sim', 'noto', 'cjk', 'chinese', 'sc', 'tc', 'jp', 'kr']
    
    for font in available_fonts:
        lower_font = font.lower()
        if any(keyword in lower_font for keyword in keywords):
            available_chinese_fonts.append(font)
            print(f"找到可能支持中文的字体: {font}")

# 测试每个可用的中文字体
if available_chinese_fonts:
    fig, axes = plt.subplots(len(available_chinese_fonts), 1, figsize=(12, len(available_chinese_fonts)*1.5))
    
    # 如果只有一个字体，确保axes是列表
    if len(available_chinese_fonts) == 1:
        axes = [axes]
    
    for i, font in enumerate(available_chinese_fonts):
        # 测试文本
        test_text = f"测试中文字体: {font} - 水库水位监测数据"
        
        # 设置字体
        axes[i].text(0.5, 0.5, test_text, 
                    fontsize=14, fontfamily=font, ha='center', va='center')
        axes[i].set_xlim(0, 1)
        axes[i].set_ylim(0, 1)
        axes[i].set_title(font, fontsize=12)
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig("chinese_fonts_test.png", dpi=150, bbox_inches='tight')
    print("\n所有中文字体预览已保存到 chinese_fonts_test.png")
    
    # 输出推荐的字体配置
    print("\n推荐的字体配置:")
    for font in available_chinese_fonts:
        print(f"""
# 使用 {font} 字体
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['{font}', 'DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = True
        """)
else:
    print("未找到任何可用的中文字体")

# 输出当前 matplotlib 的字体设置
print("\n当前 matplotlib 字体设置:")
print(f"font.family: {plt.rcParams['font.family']}")
print(f"font.sans-serif: {plt.rcParams['font.sans-serif']}")
print(f"axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")
