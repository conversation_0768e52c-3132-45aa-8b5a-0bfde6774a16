#!/usr/bin/env python
# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import os

# 查找系统中的所有字体
print("系统中的字体:")
fonts = fm.findSystemFonts()

# 优先查找Droid Sans Fallback字体（通常支持中文）
droid_fallback_fonts = [f for f in fonts if 'Droid' in f and 'Fallback' in f]
if droid_fallback_fonts:
    print(f"找到Droid Sans Fallback字体: {droid_fallback_fonts[0]}")
    font_path = droid_fallback_fonts[0]
    font_prop = fm.FontProperties(fname=font_path)
    print(f"使用字体: {font_prop.get_name()}")
else:
    # 尝试查找其他可能支持中文的字体
    chinese_fonts = []
    for f in fonts:
        if any(name in f for name in ['Droid', 'WenQuanYi', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'Microsoft YaHei']):
            chinese_fonts.append(f)

    if chinese_fonts:
        print(f"找到可能支持中文的字体: {chinese_fonts[0]}")
        font_path = chinese_fonts[0]
        font_prop = fm.FontProperties(fname=font_path)
        print(f"使用字体: {font_prop.get_name()}")
    else:
        print("未找到支持中文的字体，使用系统默认字体")
        font_prop = None

# 设置字体
if font_prop:
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    # 尝试使用系统默认sans-serif字体
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Droid Sans Fallback', 'WenQuanYi Micro Hei', 'SimHei', 'sans-serif']

# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False

# 创建一个简单的图表
x = np.linspace(0, 2*np.pi, 100)
y = np.sin(x)

plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('正弦波 (Sin Wave)')
plt.xlabel('角度 (Angle)')
plt.ylabel('振幅 (Amplitude)')
plt.grid(True)

# 添加一些中文文本
plt.text(np.pi/2, 0.5, '这是中文文本测试', fontsize=12)
plt.text(np.pi, -0.5, '负数: -123.45', fontsize=12)

# 保存图表
output_dir = 'font_test_output'
os.makedirs(output_dir, exist_ok=True)
plt.savefig(f'{output_dir}/test_chinese_font.png', dpi=100)
print(f"图表已保存到 {output_dir}/test_chinese_font.png")

# 显示所有可用的字体家族
print("\n可用的字体家族:")
for font in sorted(set([f.name for f in fm.fontManager.ttflist])):
    print(f"- {font}")

# 创建一个展示多种字体的图表
plt.figure(figsize=(12, 10))
fonts_to_test = ['Droid Sans Fallback', 'Droid Sans', 'FreeSans', 'DejaVu Sans']

# 如果找到了支持中文的字体，添加到测试列表中
if 'font_prop' in locals() and font_prop is not None:
    fonts_to_test.insert(0, font_prop.get_name())

# 去除重复项
fonts_to_test = list(dict.fromkeys(fonts_to_test))
y_positions = np.linspace(0.9, 0.1, len(fonts_to_test))

for i, font_name in enumerate(fonts_to_test):
    try:
        plt.text(0.1, y_positions[i], f'中文测试 (Chinese Test) - {font_name}',
                 fontdict={'family': font_name, 'size': 14})
    except Exception as e:
        print(f"无法使用字体: {font_name}, 错误: {str(e)}")

plt.axis('off')
plt.savefig(f'{output_dir}/font_comparison.png', dpi=100)
print(f"字体比较图已保存到 {output_dir}/font_comparison.png")

# 如果找到了Droid Sans Fallback字体，创建一个使用该字体的示例图
if 'font_prop' in locals() and font_prop is not None and 'Fallback' in font_prop.get_name():
    plt.figure(figsize=(10, 6))
    plt.plot(x, y)
    plt.title('正弦波 (Sin Wave)', fontproperties=font_prop)
    plt.xlabel('角度 (Angle)', fontproperties=font_prop)
    plt.ylabel('振幅 (Amplitude)', fontproperties=font_prop)
    plt.grid(True)
    plt.text(np.pi/2, 0.5, '这是中文文本测试', fontsize=12, fontproperties=font_prop)
    plt.text(np.pi, -0.5, '负数: -123.45', fontsize=12, fontproperties=font_prop)
    plt.savefig(f'{output_dir}/chinese_font_example.png', dpi=100)
    print(f"使用Droid Sans Fallback字体的示例图已保存到 {output_dir}/chinese_font_example.png")
