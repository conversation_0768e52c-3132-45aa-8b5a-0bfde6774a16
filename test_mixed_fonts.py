#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试中英文混合字体支持
"""

import os
import sys
import matplotlib.pyplot as plt
import numpy as np

# 导入中文字体支持模块
import chinese_font_support

def test_mixed_fonts():
    """测试中英文混合字体在不同场景下的显示效果"""

    # 创建输出目录
    output_dir = 'font_test_output'
    os.makedirs(output_dir, exist_ok=True)

    # 生成测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)

    # 测试1: 简单折线图
    plt.figure(figsize=(12, 6))
    plt.plot(x, y1, 'b-', label='正弦 Sine')
    plt.plot(x, y2, 'r--', label='余弦 Cosine')
    plt.title('中英文混合标题 Mixed Title', fontproperties=chinese_font_support.chinese_font)
    plt.xlabel('X轴 X-Axis', fontproperties=chinese_font_support.chinese_font)
    plt.ylabel('Y轴 Y-Axis', fontproperties=chinese_font_support.chinese_font)
    plt.legend(prop=chinese_font_support.chinese_font)
    plt.grid(True)
    plt.savefig(f'{output_dir}/test1_line_chart.png', dpi=150)
    plt.close()

    # 测试2: 柱状图
    categories = ['类别A\nCategory A', '类别B\nCategory B', '类别C\nCategory C', '类别D\nCategory D']
    values = [25, 40, 30, 55]

    plt.figure(figsize=(10, 6))
    bars = plt.bar(categories, values, color=['blue', 'green', 'red', 'purple'])

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                 f'{height}',
                 ha='center', va='bottom')

    plt.title('中英文混合柱状图 Mixed Bar Chart', fontproperties=chinese_font_support.chinese_font)
    plt.xlabel('类别 Categories', fontproperties=chinese_font_support.chinese_font)
    plt.ylabel('数值 Values', fontproperties=chinese_font_support.chinese_font)
    plt.savefig(f'{output_dir}/test2_bar_chart.png', dpi=150)
    plt.close()

    # 测试3: 散点图
    n = 50
    x = np.random.rand(n)
    y = np.random.rand(n)
    colors = np.random.rand(n)
    area = (30 * np.random.rand(n))**2

    plt.figure(figsize=(10, 6))
    plt.scatter(x, y, s=area, c=colors, alpha=0.5)
    plt.title('中英文混合散点图 Mixed Scatter Plot', fontproperties=chinese_font_support.chinese_font)
    plt.xlabel('X轴 (随机值) X-Axis (Random Values)', fontproperties=chinese_font_support.chinese_font)
    plt.ylabel('Y轴 (随机值) Y-Axis (Random Values)', fontproperties=chinese_font_support.chinese_font)

    # 添加文本注释
    plt.annotate('重要点 Important Point',
                xy=(0.5, 0.5), xycoords='data',
                xytext=(0.8, 0.8), textcoords='data',
                arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=.2"),
                fontproperties=chinese_font_support.chinese_font)

    plt.savefig(f'{output_dir}/test3_scatter_plot.png', dpi=150)
    plt.close()

    # 测试4: 子图组合
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))

    # 创建新的测试数据，确保维度匹配
    x_test = np.linspace(0, 10, 50)  # 使用与前面散点图相同的长度
    y_test1 = np.sin(x_test)
    y_test2 = np.cos(x_test)

    # 子图1: 折线图
    axs[0, 0].plot(x_test, y_test1)
    axs[0, 0].set_title('折线图 Line Plot', fontproperties=chinese_font_support.chinese_font)
    axs[0, 0].set_xlabel('X轴 X-Axis', fontproperties=chinese_font_support.chinese_font)
    axs[0, 0].set_ylabel('Y轴 Y-Axis', fontproperties=chinese_font_support.chinese_font)

    # 子图2: 柱状图
    axs[0, 1].bar(['A', 'B', 'C', 'D'], [10, 20, 15, 25])
    axs[0, 1].set_title('柱状图 Bar Chart', fontproperties=chinese_font_support.chinese_font)
    axs[0, 1].set_xlabel('类别 Categories', fontproperties=chinese_font_support.chinese_font)
    axs[0, 1].set_ylabel('数值 Values', fontproperties=chinese_font_support.chinese_font)

    # 子图3: 散点图
    random_data = np.random.rand(20)
    axs[1, 0].scatter(random_data, random_data * 2)
    axs[1, 0].set_title('散点图 Scatter Plot', fontproperties=chinese_font_support.chinese_font)
    axs[1, 0].set_xlabel('X轴 X-Axis', fontproperties=chinese_font_support.chinese_font)
    axs[1, 0].set_ylabel('Y轴 Y-Axis', fontproperties=chinese_font_support.chinese_font)

    # 子图4: 饼图
    axs[1, 1].pie([15, 30, 45, 10], labels=['A', 'B', 'C', 'D'], autopct='%1.1f%%')
    axs[1, 1].set_title('饼图 Pie Chart', fontproperties=chinese_font_support.chinese_font)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/test4_subplots.png', dpi=150)
    plt.close()

    print(f"所有测试图表已保存到 {output_dir} 目录")

if __name__ == "__main__":
    test_mixed_fonts()
    # 也运行中文字体支持模块中的测试函数
    chinese_font_support.test_chinese_font()
